package ru.sbertroika.tkp3.pro.manifest.processing.output.service.impl

import arrow.core.Either
import io.grpc.ManagedChannelBuilder
import org.springframework.beans.factory.annotation.Value
import org.springframework.stereotype.Service
import ru.sbertroika.abt.gateway.v1.AbtGatewayServiceGrpcKt
import ru.sbertroika.common.manifest.v1.manifestRequest
import ru.sbertroika.common.toZonedDateTime
import ru.sbertroika.tkp3.manifest.model.FeatureState
import ru.sbertroika.tkp3.manifest.model.ManifestProAbt
import ru.sbertroika.tkp3.manifest.model.ManifestProAbtDict
import ru.sbertroika.tkp3.manifest.model.TkpFeature
import ru.sbertroika.tkp3.manifest.model.pro.*
import ru.sbertroika.tkp3.pro.manifest.processing.output.service.AbtService
import java.util.*
import java.util.concurrent.TimeUnit

@Service
class AbtServiceImpl(
    @Value("\${abt_gateway_url}")
    private val gateUrl: String
) : AbtService {

    private val channel = ManagedChannelBuilder.forTarget(gateUrl)
        .usePlaintext()
        .idleTimeout(60000, TimeUnit.MILLISECONDS)
        .keepAliveTimeout(60000, TimeUnit.MILLISECONDS)
        .enableRetry()
        .maxRetryAttempts(3)
        .build()

    val client = AbtGatewayServiceGrpcKt.AbtGatewayServiceCoroutineStub(channel)

    override suspend fun getManifest(projectId: String): Either<Throwable, ManifestProAbt> = Either.catch {
        val response = client.getManifest(
            manifestRequest {
                this.projectId = projectId
            }
        )

        if (response.hasError()) throw Error(response.error.message)

        ManifestProAbt(
            features = response.manifest.featuresList.map {
                TkpFeature(
                    name = it.name,
                    state = FeatureState.valueOf(it.state.name)
                )
            }.toList(),
            dict = ManifestProAbtDict(
                templates = response.manifest.dict.templateList.map { template ->
                    AbtTemplate(
                        id = UUID.fromString(template.id),
                        version = template.version,
                        appCode = template.appCode,
                        crdCode = template.crdCode,
                        name = template.name,
                        type = AbonementType.valueOf(template.type.name),
                        isSocial = template.isSocial,
                        rules = template.rulesList.map { rule ->
                            AbtPassRule(
                                id = UUID.fromString(rule.id),
                                version = rule.version,
                                index = rule.index,
                                action = rule.action
                            )
                        }.toList(),
                        counters = template.counterList.map { counter ->
                            AbtTemplateCounter(
                                id = UUID.fromString(counter.id),
                                version = counter.version,
                                type = SubscriptionCounterType.valueOf(counter.type.name),
                                value = counter.value,
                                isBus = counter.isBus,
                                isTrolleybus = counter.isTrolleybus,
                                isTram = counter.isTram,
                                isMetro = counter.isMetro
                            )
                        }.toList(),
                        cardTypes = template.cardTypeList.map { cardType ->
                            AbtCardType(
                                id = UUID.fromString(cardType.id)
                            )
                        }.toList(),
                        validTimeType = ValidTimeType.valueOf(template.validTimeType.name),
                        validTimeStart = template.validTimeStart.toZonedDateTime(),
                        validTimeEnd = template.validTimeEnd.toZonedDateTime(),
                        validTimeDays = template.validTimeDays
                    )
                }.toList()
            )
        )
    }
}