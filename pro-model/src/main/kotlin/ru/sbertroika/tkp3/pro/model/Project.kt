package ru.sbertroika.tkp3.pro.model

import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.sql.Timestamp
import java.time.LocalDate
import java.util.*

data class ProjectPK(
    val pId: UUID? = null,
    val pVersion: Int? = null
)

@Table("project")
data class Project(

    @Column("pr_id")
    var id: UUID? = null,

    @Column("pr_version")
    var version: Int? = null,

    @Column("pr_version_created_at")
    var versionCreatedAt: Timestamp? = null,

    @Column("pr_version_created_by")
    var versionCreatedBy: UUID? = null,

    @Column("pr_start_date")
    var startDate: LocalDate? = null,

    @Column("pr_end_date")
    var endDate: LocalDate? = null,

    @Column("pr_name")
    var name: String? = null,

    @Column("pr_status")
    var status: ProjectStatus? = null,

    @Column("pr_contract_id")
    var contractId: UUID? = null,

    @Column("pr_contract_number")
    var contractNumber: String? = null,

    @Column("pr_description")
    var description: String? = null,

    @Column("pr_region")
    var region: String? = null,

    /**
     * Порядковый номер проекта в системе СТ
     */
    @Column("p_index")
    var index: Int? = null,

    @Column("tags")
    var tags: String? = null
)

enum class ProjectStatus {
    TEST, DEMO, ACTIVE, ARCHIVE, NSI_CONFLICT, DISABLED, BLOCKED, IS_DELETED
}