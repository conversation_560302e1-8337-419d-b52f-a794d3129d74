package ru.sbertroika.tkp3.pro.model

import org.springframework.data.relational.core.mapping.Column
import org.springframework.data.relational.core.mapping.Table
import java.io.Serializable
import java.sql.Timestamp
import java.util.*

@Table("station")
data class Station(

    @Column("st_id")
    var id: UUID? = null,

    @Column("st_version")
    var version: Int? = null,

    @Column("st_version_created_at")
    var versionCreatedAt: Timestamp? = null,

    @Column("st_version_created_by")
    var versionCreatedBy: UUID? = null,

    @Column("p_id")
    var projectId: UUID? = null,

    @Column("tz_id")
    var tariffZoneId: UUID? = null,

    @Column("st_name")
    var name: String? = null,

    @Column("st_status")
    var status: StationStatus? = null,

    @Column("lat")
    var latitude: Double? = null,

    @Column("long")
    var longitude: Double? = null,

    @Column("tags")
    var tags: String? = null
)

data class StationPK(
    val stId: UUID? = null,
    val stVersion: Int? = null,
) : Serializable

enum class StationStatus {
    ACTIVE, DISABLED, BLOCKED, IS_DELETED
}