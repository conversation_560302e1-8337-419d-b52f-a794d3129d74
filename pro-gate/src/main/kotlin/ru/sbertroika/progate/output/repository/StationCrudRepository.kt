package ru.sbertroika.progate.output.repository

import ru.sbertroika.tkp3.pro.common.repository.AbstractSettingsRepository
import io.r2dbc.spi.Readable
import kotlinx.coroutines.flow.Flow
import org.springframework.beans.factory.annotation.Qualifier
import org.springframework.data.repository.kotlin.CoroutineCrudRepository
import org.springframework.r2dbc.core.DatabaseClient
import org.springframework.r2dbc.core.awaitOneOrNull
import org.springframework.r2dbc.core.flow
import org.springframework.stereotype.Component
import ru.sbertroika.progate.util.timestampNow
import ru.sbertroika.tkp3.pro.model.Station
import ru.sbertroika.tkp3.pro.model.StationPK
import ru.sbertroika.tkp3.pro.model.StationStatus
import java.util.*

interface StationCrudRepository : CoroutineCrudRepository<Station, StationPK> {

    suspend fun findAllByProjectId(projectId: UUID): Flow<Station>
}


@Component
class StationRepository(
    override val dbClient: DatabaseClient,
    override val repository: StationCrudRepository
): AbstractSettingsRepository<Station, StationPK>(dbClient, repository){
    override fun getQuery(isCount: Boolean): String = (if (isCount) "SELECT COUNT(*) \n"
    else "SELECT * \n") +
            "FROM station o\n" +
            "INNER JOIN (\n" +
            "    SELECT st_id, MAX(st_version) vers\n" +
            "    FROM station\n" +
            "    GROUP BY st_id\n" +
            ") o2 ON o.st_id = o2.st_id AND o.st_version = o2.vers AND o.st_status != 'IS_DELETED'"

    override fun toEntity(t: Readable) = Station(
        id = t.get("st_id") as UUID,
        version = t.get("st_version") as Int,
        name = t.get("st_name") as String,
        tags = t.get("tags") as String?,
        status = t.get("st_status") as StationStatus,
        projectId = t.get("p_id") as UUID?,
        tariffZoneId = t.get("tz_id") as UUID?,
        latitude = t.get("lat") as Double?,
        longitude = t.get("long") as Double?
    )

    override suspend fun findById(id: String): Station? {
        return dbClient.sql("${getQuery()} AND o.st_id = '$id'")
            .map(::toEntity).awaitOneOrNull()
    }

    override fun findAll(page: Int, limit: Int): Flow<Station> {
        return dbClient.sql(getQuery())
            .map(::toEntity).flow()
    }

    override fun findAll(): Flow<Station> {
        return dbClient.sql(getQuery())
            .map(::toEntity).flow()
    }

    fun addNameFilter(query: String, number : String) = "$query AND o.st_name LIKE '%$number%'"

    fun findAllByRequest(query: String): Flow<Station> {
        return dbClient.sql(query)
            .map(::toEntity).flow()
    }

    override suspend fun deleted(id: String, userId: UUID) {
        val entity = findById(id)
        if(entity != null && entity.status != StationStatus.IS_DELETED) {
            repository.save(entity.copy(
                version = entity.version!! + 1,
                versionCreatedBy = userId,
                versionCreatedAt = timestampNow(),
                status = StationStatus.IS_DELETED
            ))
        }
    }

}

