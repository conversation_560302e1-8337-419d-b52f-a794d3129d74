FROM gradle:8.13-jdk17-alpine as builder
ARG GRADLE_USER_HOME=/tmp/.gradle
ENV GRADLE_USER_HOME=$GRADLE_USER_HOME
RUN apk add gcompat

WORKDIR /build
ADD . /build

COPY .ci-gradle/gradle.properties /tmp/.gradle/gradle.properties
COPY .ci-gradle/gradle.properties /home/<USER>/.gradle/gradle.properties
COPY .ci-gradle/gradle.properties /root/.gradle/gradle.properties

RUN env

RUN gradle --no-daemon :pro-gate-private:bootJar -i

FROM openjdk:17-alpine
COPY --from=builder /build/pro-gate-private/build/libs/pro-gate-private-*.jar ./pro-gate-private.jar

EXPOSE 5000
EXPOSE 8080
EXPOSE 6000

ENTRYPOINT ["java", "-agentlib:jdwp=transport=dt_socket,server=y,suspend=n,address=*:6000", "-Djava.security.egd=file:/dev/./urandom", "-jar", "pro-gate-private.jar"]
