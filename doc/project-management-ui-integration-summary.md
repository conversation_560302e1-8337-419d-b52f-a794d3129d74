# Корректировка тестов управления проектами - Интеграция через UI

## Обзор изменений

Тестовые сценарии были скорректированы для отражения того факта, что данная АС не имеет прямого взаимодействия с системой 1С. Все данные договоров поступают через пользовательский интерфейс.

## Ключевые корректировки

### 🔄 Изменения в источнике данных

**Было:**
- Прямое взаимодействие с системой 1С
- Автоматический импорт договоров из 1С
- Синхронизация с внешней системой

**Стало:**
- Данные договоров предоставляются через пользовательский интерфейс
- Пользователь вводит данные договора вручную
- Федеральный идентификатор для связи с внешними системами

### 📝 Обновленные сценарии

1. **Создание проекта:**
   - `Дано пользователь предоставил данные договора:`
   - `Когда я создаю новый проект на основе предоставленных данных договора`

2. **Работа с договорами:**
   - `И пользователь предоставил данные нового договора`
   - `И пользователь ввел данные договора:`
   - `Когда пользователь указывает наименование проекта`

3. **Обновление данных:**
   - `И пользователь предоставил обновленные данные договора`
   - `Когда я обновляю проект согласно новым данным договора`

4. **UI взаимодействие:**
   - `Дано пользователь открыл форму создания проекта`
   - `И пользователь подтверждает создание проекта`

### 🗂️ Модель данных договора

```kotlin
data class ContractData(
    val id: String? = null,
    val номер_договора: String,
    val название_договора: String,
    val дата_начала: LocalDate,
    val дата_окончания: LocalDate,
    val федеральный_id: String,  // Изменено с внешний_id_1с
    val статус: String = "ACTIVE"
)
```

### 🔧 Обновленные методы тестирования

**Новые методы:**
- `пользователь_предоставил_данные_договора()`
- `пользователь_открыл_форму_создания_проекта()`
- `пользователь_ввел_данные_договора()`
- `пользователь_подтверждает_создание_проекта()`
- `пользователь_предоставил_обновленные_данные_договора_с_новыми_сроками()`

**Удаленные методы:**
- `в_системе_1с_существуют_договоры()`
- `система_импортирует_договор_из_1с()`
- `связанный_договор_был_обновлен_в_системе_1с()`

## Архитектурные принципы

### 📊 Поток данных
```
Пользователь → UI → АС → База данных
     ↓
Ввод данных договора
     ↓
Создание/обновление проекта
     ↓
Управление жизненным циклом
```

### 🔐 Безопасность и валидация
- Валидация данных на уровне UI
- Проверка корректности федерального идентификатора
- Валидация наименований регионов
- Контроль уникальности проектов

### 📋 Бизнес-логика (без изменений)
- State machine для статусов проекта
- Наследование дат от договора
- Блокировка операций для неактивных статусов
- Обработка конфликтов НСИ

## Преимущества нового подхода

### ✅ Гибкость
- Независимость от внешних систем
- Контроль пользователя над данными
- Возможность корректировки данных

### ✅ Надежность
- Отсутствие зависимости от доступности 1С
- Контролируемый ввод данных
- Возможность валидации на UI

### ✅ Простота
- Упрощенная архитектура
- Меньше точек отказа
- Прозрачный процесс создания проектов

## Тестовое покрытие

### 🧪 Покрытые сценарии:
- ✅ Ввод данных договора через UI
- ✅ Создание проекта на основе введенных данных
- ✅ Обновление проекта при изменении данных договора
- ✅ Валидация входных данных
- ✅ Работа с формами пользовательского интерфейса
- ✅ Управление жизненным циклом проекта
- ✅ Обработка ошибок и исключений

### 🔍 Негативные сценарии:
- ✅ Создание проекта без данных договора
- ✅ Некорректные данные договора
- ✅ Дублирование наименований проектов
- ✅ Недопустимые переходы статусов

## Следующие шаги

1. **Реализация UI компонентов** для ввода данных договора
2. **Создание API endpoints** для работы с договорами
3. **Реализация валидации** на backend
4. **Создание моделей данных** в соответствии с тестами
5. **Интеграция с существующей логикой** управления проектами

## Заключение

Корректировка тестов отражает реальную архитектуру системы, где данные поступают через пользовательский интерфейс, а не из внешних систем. Это обеспечивает большую гибкость и контроль над процессом управления проектами, сохраняя при этом всю необходимую бизнес-логику.
