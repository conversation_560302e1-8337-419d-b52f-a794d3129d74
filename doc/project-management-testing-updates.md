# Обновление тестовых сценариев управления проектами

## Обзор изменений

Тестовые сценарии для управления проектами были полностью переработаны в соответствии с новыми требованиями к системе управления проектами СберТройка ПРО.

## Ключевые изменения

### 1. Новая модель создания проектов
- **Было**: Проекты создавались напрямую с указанием всех параметров
- **Стало**: Проекты создаются на основе данных договоров, предоставляемых через пользовательский интерфейс
- **Обязательные поля**: Только наименование региона (вводится пользователем)
- **Автоматические поля**: Даты начала и окончания наследуются от договора
- **Источник данных**: Пользовательский интерфейс (без прямого взаимодействия с 1С)

### 2. Новая система статусов проекта
Реализована state machine со следующими статусами:

#### Статусы:
- **TEST** - Начальный статус (по умолчанию для всех новых проектов)
- **DEMO** - Демонстрационный статус
- **ACTIVE** - Промышленное использование (единственный статус для боевых операций)
- **ARCHIVE** - Завершенные/недействительные проекты
- **NSI_CONFLICT** - Конфликт нормативно-справочной информации

#### Правила переходов:
```
TEST → DEMO, ACTIVE
DEMO → ACTIVE
ACTIVE → ARCHIVE
* → NSI_CONFLICT (из любого статуса)
NSI_CONFLICT → TEST (после устранения конфликта)
```

### 3. Бизнес-правила и ограничения
- **Промышленные операции** разрешены только в статусе ACTIVE
- **Демонстрационные операции** разрешены в статусах DEMO и ACTIVE
- **Все операции заблокированы** в статусах ARCHIVE и NSI_CONFLICT
- **Валидация наименования** проекта по названию региона
- **Уникальность наименований** проектов в рамках системы

### 4. Работа с данными договоров через UI
- **Ввод данных договора** через пользовательский интерфейс
- **Обновление данных** проекта при изменении договора
- **Федеральный идентификатор** договора для связи с внешними системами
- **Отслеживание изменений** в договорах и их влияние на проекты

## Обновленные файлы

### 1. Файл сценариев
**Путь**: `pro-gate-private/src/test/resources/features/управление_проектами.feature`

**Новые сценарии**:
- Создание проекта на основе данных договора, предоставленных через UI
- Переходы между статусами согласно state machine
- Обнаружение и разрешение конфликтов НСИ
- Валидация бизнес-правил для разных статусов
- Обновление проектов при изменении данных договора
- Создание проектов через пользовательский интерфейс

### 2. Файл шагов тестирования
**Путь**: `pro-gate-private/src/test/kotlin/ru/sbertroika/progate/priv/cucumber/steps/УправлениеПроектамиSteps.kt`

**Обновления**:
- Новые модели данных `ProjectData` и `ContractData` (с полем `федеральный_id`)
- Enum `ProjectStatus` с новыми статусами
- Методы для работы с данными договоров через UI
- Валидация переходов между статусами
- Проверка бизнес-правил и ограничений
- Симуляция конфликтов НСИ и их разрешения
- Методы для работы с пользовательским интерфейсом

## Покрытие тестами

### Функциональные сценарии:
1. ✅ Создание проекта на основе данных договора через UI
2. ✅ Управление жизненным циклом проекта (state machine)
3. ✅ Валидация бизнес-правил
4. ✅ Обработка конфликтов НСИ
5. ✅ Обновление проектов при изменении данных договора
6. ✅ Поиск и фильтрация проектов
7. ✅ Работа с пользовательским интерфейсом

### Негативные сценарии:
1. ✅ Недопустимые переходы статусов
2. ✅ Дублирование наименований проектов
3. ✅ Создание проекта без предоставления данных договора
4. ✅ Некорректные наименования регионов
5. ✅ Блокировка операций для неактивных проектов

## Следующие шаги

После обновления тестовых сценариев необходимо:

1. **Реализовать модели данных** в backend (Project, Contract)
2. **Создать миграции БД** для новых таблиц
3. **Реализовать сервисы** для управления проектами и договорами
4. **Создать контроллеры** для API
5. **Обновить UI** для работы с новой логикой
6. **Запустить тесты** и убедиться в их прохождении

## Консистентность mock-данных

Все mock-данные в тестах поддерживают глобальную консистентность:
- Договоры имеют уникальные номера и федеральные идентификаторы
- Проекты связаны с конкретными договорами
- Статусы проектов соответствуют бизнес-правилам
- Даты проектов наследуются от договоров
- Наименования проектов соответствуют регионам
- Данные договоров предоставляются через пользовательский интерфейс

## Комментарии в коде

Все методы в тестовых шагах содержат комментарии о назначении:
- TODO комментарии указывают на места для интеграции с реальными сервисами
- Описания бизнес-логики для каждого метода
- Пояснения по валидации и проверкам
