export const TerminalService = {
    getData: () => {
        return [
            {
                id: 1,
                serialNumber: 'TRM-MSK-001',
                model: 'SberTroika Terminal v2.1',
                location: 'Автобус №101, маршрут М1',
                status: 'online',
                softwareVersion: '2.1.15',
                configurationVersion: '1.0.8',
                lastSeen: '2024-01-20T14:30:00Z',
                batteryLevel: 85,
                networkSignal: 'excellent',
                organizationId: 1,
                organizationName: 'ООО "Городской транспорт"',
                driverId: 'DRV001',
                driverName: 'Иванов И.И.',
                vehicleId: 'BUS101',
                vehiclePlate: 'А123БВ77',
                createdDate: '2023-12-01T09:00:00Z'
            },
            {
                id: 2,
                serialNumber: 'TRM-MSK-002',
                model: 'SberTroika Terminal v2.1',
                location: 'Станция метро "Сокольники"',
                status: 'offline',
                softwareVersion: '2.1.12',
                configurationVersion: '1.0.7',
                lastSeen: '2024-01-20T12:15:00Z',
                batteryLevel: 0,
                networkSignal: 'none',
                organizationId: 2,
                organizationName: 'АО "Метрополитен"',
                driverId: null,
                driverName: null,
                vehicleId: null,
                vehiclePlate: null,
                createdDate: '2023-11-15T10:30:00Z'
            },
            {
                id: 3,
                serialNumber: 'TRM-MSK-003',
                model: 'SberTroika Terminal v2.0',
                location: 'Автобус №205, маршрут М5',
                status: 'maintenance',
                softwareVersion: '2.0.22',
                configurationVersion: '1.0.6',
                lastSeen: '2024-01-19T16:45:00Z',
                batteryLevel: 45,
                networkSignal: 'good',
                organizationId: 3,
                organizationName: 'ООО "Автобусный парк №1"',
                driverId: 'DRV003',
                driverName: 'Петров П.П.',
                vehicleId: 'BUS205',
                vehiclePlate: 'В456ГД77',
                createdDate: '2023-10-20T14:20:00Z'
            },
            {
                id: 4,
                serialNumber: 'TRM-MSK-004',
                model: 'SberTroika Terminal v2.1',
                location: 'Троллейбус №12, маршрут Т3',
                status: 'online',
                softwareVersion: '2.1.15',
                configurationVersion: '1.0.8',
                lastSeen: '2024-01-20T14:25:00Z',
                batteryLevel: 92,
                networkSignal: 'excellent',
                organizationId: 4,
                organizationName: 'ГУП "Мосгортранс"',
                driverId: 'DRV004',
                driverName: 'Сидоров С.С.',
                vehicleId: 'TRL012',
                vehiclePlate: 'Г789ЕЖ77',
                createdDate: '2024-01-05T11:10:00Z'
            },
            {
                id: 5,
                serialNumber: 'TRM-MSK-005',
                model: 'SberTroika Terminal v2.1',
                location: 'Касса метро "Красные ворота"',
                status: 'warning',
                softwareVersion: '2.1.14',
                configurationVersion: '1.0.8',
                lastSeen: '2024-01-20T14:28:00Z',
                batteryLevel: 100,
                networkSignal: 'good',
                organizationId: 2,
                organizationName: 'АО "Метрополитен"',
                driverId: null,
                driverName: null,
                vehicleId: null,
                vehiclePlate: null,
                createdDate: '2023-12-10T13:45:00Z'
            }
        ];
    },

    getTerminals() {
        return Promise.resolve(this.getData());
    },

    getTerminalById(id) {
        const terminals = this.getData();
        return Promise.resolve(terminals.find(terminal => terminal.id === parseInt(id)));
    },

    getTerminalsByOrganization(organizationId) {
        const terminals = this.getData();
        return Promise.resolve(terminals.filter(terminal => terminal.organizationId === parseInt(organizationId)));
    },

    createTerminal(terminalData) {
        console.log('Creating terminal:', terminalData);
        
        const newTerminal = {
            ...terminalData,
            id: Date.now(),
            status: 'offline',
            lastSeen: null,
            batteryLevel: 0,
            networkSignal: 'none',
            createdDate: new Date().toISOString()
        };
        
        return Promise.resolve(newTerminal);
    },

    updateTerminal(id, terminalData) {
        console.log('Updating terminal:', id, terminalData);
        
        const updatedTerminal = {
            ...terminalData,
            id: parseInt(id)
        };
        
        return Promise.resolve(updatedTerminal);
    },

    deleteTerminal(id) {
        console.log('Deleting terminal:', id);
        return Promise.resolve({ success: true });
    },

    updateSoftware(terminalId, softwareVersion) {
        console.log('Updating software for terminal:', terminalId, 'to version:', softwareVersion);
        return Promise.resolve({ 
            success: true, 
            message: `ПО терминала ${terminalId} обновлено до версии ${softwareVersion}`,
            updateDate: new Date().toISOString()
        });
    },

    updateConfiguration(terminalId, configurationVersion) {
        console.log('Updating configuration for terminal:', terminalId, 'to version:', configurationVersion);
        return Promise.resolve({ 
            success: true, 
            message: `Конфигурация терминала ${terminalId} обновлена до версии ${configurationVersion}`,
            updateDate: new Date().toISOString()
        });
    },

    getTerminalTelemetry(terminalId) {
        console.log('Getting telemetry for terminal:', terminalId);
        return Promise.resolve({
            terminalId: terminalId,
            timestamp: new Date().toISOString(),
            batteryLevel: Math.floor(Math.random() * 100),
            networkSignal: ['excellent', 'good', 'poor', 'none'][Math.floor(Math.random() * 4)],
            temperature: Math.floor(Math.random() * 40) + 10,
            memoryUsage: Math.floor(Math.random() * 80) + 20,
            diskUsage: Math.floor(Math.random() * 60) + 30,
            transactionsToday: Math.floor(Math.random() * 500),
            lastTransaction: new Date(Date.now() - Math.random() * 3600000).toISOString()
        });
    },

    restartTerminal(terminalId) {
        console.log('Restarting terminal:', terminalId);
        return Promise.resolve({ 
            success: true, 
            message: `Команда перезагрузки отправлена на терминал ${terminalId}`,
            commandDate: new Date().toISOString()
        });
    }
};
