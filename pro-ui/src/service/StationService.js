export const StationService = {
    getData: () => {
        return [
            {
                id: 1,
                name: 'Центральный автовокзал',
                latinName: 'Central Bus Station',
                city: 'Москва',
                district: 'Центральный',
                region: 'Московская область',
                country: 'Россия',
                hasCoordinates: true,
                coordinates: '55.7558, 37.6176',
                projectId: 1 // Привязка к проекту
            },
            {
                id: 2,
                name: 'Площадь Революции',
                latinName: 'Revolution Square',
                city: 'Москва',
                district: 'Тверской',
                region: 'Московская область',
                country: 'Россия',
                hasCoordinates: true,
                coordinates: '55.7567, 37.6231',
                projectId: 1 // Привязка к проекту
            },
            {
                id: 3,
                name: 'Красная площадь',
                latinName: 'Red Square',
                city: 'Москва',
                district: 'Тверской',
                region: 'Московская область',
                country: 'Россия',
                hasCoordinates: false,
                coordinates: null,
                projectId: 1 // Привязка к проекту
            },
            {
                id: 4,
                name: 'Невский проспект',
                latinName: 'Nevsky Prospect',
                city: 'Санкт-Петербург',
                district: 'Центральный',
                region: 'Ленинградская область',
                country: 'Россия',
                hasCoordinates: true,
                coordinates: '59.9311, 30.3609',
                projectId: 2 // Привязка к проекту Метрополитен
            },
            {
                id: 5,
                name: 'Дворцовая площадь',
                latinName: 'Palace Square',
                city: 'Санкт-Петербург',
                district: 'Центральный',
                region: 'Ленинградская область',
                country: 'Россия',
                hasCoordinates: true,
                coordinates: '59.9387, 30.3162',
                projectId: 2 // Привязка к проекту Метрополитен
            },
            {
                id: 6,
                name: 'Площадь Ленина',
                latinName: 'Lenin Square',
                city: 'Екатеринбург',
                district: 'Ленинский',
                region: 'Свердловская область',
                country: 'Россия',
                hasCoordinates: false,
                coordinates: null,
                projectId: 3 // Привязка к проекту Автобусы
            },
            {
                id: 7,
                name: 'Театральная площадь',
                latinName: 'Theatre Square',
                city: 'Новосибирск',
                district: 'Центральный',
                region: 'Новосибирская область',
                country: 'Россия',
                hasCoordinates: true,
                coordinates: '55.0415, 82.9346',
                projectId: 3 // Привязка к проекту Автобусы
            },
            {
                id: 8,
                name: 'Вокзальная площадь',
                latinName: 'Station Square',
                city: 'Казань',
                district: 'Вахитовский',
                region: 'Республика Татарстан',
                country: 'Россия',
                hasCoordinates: true,
                coordinates: '55.7887, 49.1221',
                projectId: 1 // Привязка к основному проекту
            },
            {
                id: 9,
                name: 'Площадь Свободы',
                latinName: 'Freedom Square',
                city: 'Нижний Новгород',
                district: 'Нижегородский',
                region: 'Нижегородская область',
                country: 'Россия',
                hasCoordinates: true,
                coordinates: '56.3287, 44.0020',
                projectId: 4 // Привязка к проекту
            },
            {
                id: 10,
                name: 'Центральная площадь',
                latinName: 'Central Square',
                city: 'Самара',
                district: 'Самарский',
                region: 'Самарская область',
                country: 'Россия',
                hasCoordinates: false,
                coordinates: null,
                projectId: 5 // Привязка к проекту
            },
            {
                id: 11,
                name: 'Привокзальная площадь',
                latinName: 'Railway Station Square',
                city: 'Волгоград',
                district: 'Центральный',
                region: 'Волгоградская область',
                country: 'Россия',
                hasCoordinates: true,
                coordinates: '48.7194, 44.5018',
                projectId: 6 // Привязка к проекту
            },
            {
                id: 12,
                name: 'Площадь Победы',
                latinName: 'Victory Square',
                city: 'Воронеж',
                district: 'Центральный',
                region: 'Воронежская область',
                country: 'Россия',
                hasCoordinates: true,
                coordinates: '51.6605, 39.2006',
                projectId: 7 // Привязка к проекту
            }
        ]
    },

    getStations() {
        return Promise.resolve(this.getData());
    },

    getStationsByProject(projectId) {
        const allStations = this.getData();
        const projectStations = allStations.filter(station => station.projectId == projectId);
        return Promise.resolve(projectStations);
    },

    getStationsByIds(stationIds) {
        const allStations = this.getData();
        const filteredStations = allStations.filter(station => stationIds.includes(station.id));
        return Promise.resolve(filteredStations);
    },

    getStationById(stationId) {
        const stations = this.getData();
        const station = stations.find(s => s.id == stationId);
        return Promise.resolve(station);
    },

    createStation(projectCode, stationData) {
        // В реальном приложении здесь будет отправка POST запроса
        console.log('Creating station for project:', projectCode, stationData);

        // Имитация создания с новым ID
        const newStation = {
            ...stationData,
            id: Date.now() // Временный ID для демонстрации
        };

        return Promise.resolve(newStation);
    },

    updateStation(stationId, stationData) {
        // В реальном приложении здесь будет отправка PUT запроса
        console.log('Updating station:', stationId, stationData);

        const updatedStation = {
            ...stationData,
            id: stationId
        };

        return Promise.resolve(updatedStation);
    },

    deleteStation(stationId) {
        // В реальном приложении здесь будет отправка DELETE запроса
        console.log('Deleting station:', stationId);
        return Promise.resolve({ success: true });
    }
}
