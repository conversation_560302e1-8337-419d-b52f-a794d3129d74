export const OrganizationService = {
    getData: () => {
        return [
            {
                id: 1,
                type: 'organization', // organization, individual_entrepreneur
                ownershipForm: 'ООО',
                name: 'Городской транспорт',
                fullName: 'Общество с ограниченной ответственностью "Городской транспорт"',
                shortName: 'Городской транспорт',
                inn: '7701234567',
                ogrn: '1027700123456',
                kpp: '770101001',
                okpo: '12345678',
                oktmo: '45000000',
                okved: '49.31 - Деятельность городского и пригородного пассажирского наземного транспорта',
                legalAddress: '123456, г. Москва, ул. Транспортная, д. 1, стр. 1',
                actualAddress: '123456, г. Москва, ул. Транспортная, д. 1, стр. 1',
                basedOn: 'Устав',
                generalDirector: 'Иванов Иван Иванович',
                responsibleForSignature: 'Петров Петр Петрович',
                email: '<EMAIL>',
                phone: '+7 (495) 123-45-67',
                status: 'active',
                syncStatus: 'synced',
                lastSyncDate: '2024-01-15T10:30:00Z',
                createdDate: '2023-01-10T09:00:00Z'
            },
            {
                id: 2,
                type: 'organization',
                ownershipForm: 'АО',
                name: 'Метрополитен',
                fullName: 'Акционерное общество "Метрополитен"',
                shortName: 'Метрополитен',
                inn: '7702345678',
                ogrn: '1027700234567',
                kpp: '770201001',
                okpo: '23456789',
                oktmo: '45000000',
                okved: '49.31 - Деятельность городского и пригородного пассажирского наземного транспорта',
                legalAddress: '101000, г. Москва, Сокольническая площадь, д. 2',
                actualAddress: '101000, г. Москва, Сокольническая площадь, д. 2',
                basedOn: 'Устав',
                generalDirector: 'Петров Петр Петрович',
                responsibleForSignature: 'Сидоров Сидор Сидорович',
                email: '<EMAIL>',
                phone: '+7 (495) 234-56-78',
                status: 'active',
                syncStatus: 'pending',
                lastSyncDate: '2024-01-10T14:20:00Z',
                createdDate: '2023-02-15T11:30:00Z'
            },
            {
                id: 3,
                type: 'organization',
                ownershipForm: 'ООО',
                name: 'Автобусный парк №1',
                fullName: 'Общество с ограниченной ответственностью "Автобусный парк №1"',
                shortName: 'Автобусный парк №1',
                inn: '7703456789',
                ogrn: '1027700345678',
                kpp: '770301001',
                okpo: '34567890',
                oktmo: '45000000',
                okved: '49.31 - Деятельность городского и пригородного пассажирского наземного транспорта',
                legalAddress: '115230, г. Москва, ул. Автобусная, д. 15',
                actualAddress: '115230, г. Москва, ул. Автобусная, д. 15',
                basedOn: 'Устав',
                generalDirector: 'Сидоров Сидор Сидорович',
                responsibleForSignature: 'Козлов Андрей Викторович',
                email: '<EMAIL>',
                phone: '+7 (495) 345-67-89',
                status: 'active',
                syncStatus: 'error',
                lastSyncDate: '2024-01-05T16:45:00Z',
                createdDate: '2023-03-20T13:15:00Z'
            },
            {
                id: 4,
                type: 'organization',
                ownershipForm: 'ГУП',
                name: 'Мосгортранс',
                fullName: 'Государственное унитарное предприятие "Мосгортранс"',
                shortName: 'Мосгортранс',
                inn: '7704567890',
                ogrn: '1027700456789',
                kpp: '770401001',
                okpo: '45678901',
                oktmo: '45000000',
                okved: '49.31 - Деятельность городского и пригородного пассажирского наземного транспорта',
                legalAddress: '125009, г. Москва, ул. Центральная, д. 25',
                actualAddress: '125009, г. Москва, ул. Центральная, д. 25',
                basedOn: 'Устав',
                generalDirector: 'Козлов Андрей Владимирович',
                responsibleForSignature: 'Морозов Дмитрий Александрович',
                email: '<EMAIL>',
                phone: '+7 (495) 456-78-90',
                status: 'active',
                syncStatus: 'synced',
                lastSyncDate: '2024-01-16T08:15:00Z',
                createdDate: '2023-04-10T10:45:00Z'
            },
            {
                id: 5,
                type: 'organization',
                ownershipForm: 'ООО',
                name: 'Процессинг Центр',
                fullName: 'Общество с ограниченной ответственностью "Процессинг Центр"',
                shortName: 'Процессинг Центр',
                inn: '7705678901',
                ogrn: '1027700567890',
                kpp: '770501001',
                okpo: '56789012',
                oktmo: '45000000',
                okved: '66.19 - Прочие виды деятельности по предоставлению финансовых услуг',
                legalAddress: '119021, г. Москва, ул. Процессинговая, д. 10',
                actualAddress: '119021, г. Москва, ул. Процессинговая, д. 10',
                basedOn: 'Устав',
                generalDirector: 'Процессоров Процессор Процессорович',
                responsibleForSignature: 'Платежов Платеж Платежович',
                email: '<EMAIL>',
                phone: '+7 (495) 567-89-01',
                status: 'active',
                syncStatus: 'synced',
                lastSyncDate: '2024-01-18T09:15:00Z',
                createdDate: '2023-05-25T14:20:00Z'
            },
            {
                id: 6,
                type: 'organization',
                ownershipForm: 'АО',
                name: 'Пригородные перевозки',
                fullName: 'Акционерное общество "Пригородные перевозки"',
                shortName: 'Пригородные перевозки',
                inn: '7706789012',
                ogrn: '1027700678901',
                kpp: '770601001',
                okpo: '67890123',
                oktmo: '45000000',
                okved: '49.10 - Деятельность железнодорожного транспорта: междугородные перевозки',
                legalAddress: '125040, г. Москва, ул. Пригородная, д. 8',
                actualAddress: '125040, г. Москва, ул. Пригородная, д. 8',
                basedOn: 'Устав',
                generalDirector: 'Волков Сергей Николаевич',
                responsibleForSignature: 'Поездов Поезд Поездович',
                email: '<EMAIL>',
                phone: '+7 (495) 678-90-12',
                status: 'active',
                syncStatus: 'synced',
                lastSyncDate: '2024-01-14T12:00:00Z',
                createdDate: '2023-06-30T16:30:00Z'
            },
            {
                id: 7,
                type: 'organization',
                ownershipForm: 'ООО',
                name: 'Речфлот',
                fullName: 'Общество с ограниченной ответственностью "Речфлот"',
                shortName: 'Речфлот',
                inn: '7707890123',
                ogrn: '1027700789012',
                kpp: '770701001',
                okpo: '78901234',
                oktmo: '45000000',
                okved: '50.30 - Деятельность внутреннего водного транспорта',
                legalAddress: '115035, г. Москва, Набережная, д. 12',
                actualAddress: '115035, г. Москва, Набережная, д. 12',
                basedOn: 'Устав',
                generalDirector: 'Лебедев Михаил Игоревич',
                responsibleForSignature: 'Речников Речник Речникович',
                email: '<EMAIL>',
                phone: '+7 (495) 789-01-23',
                status: 'active',
                syncStatus: 'pending',
                lastSyncDate: '2024-01-12T09:30:00Z',
                createdDate: '2023-07-15T14:00:00Z'
            },
            {
                id: 8,
                name: 'ООО "Грузоперевозки Север"',
                shortName: 'Грузоперевозки Север',
                inn: '7708901234',
                kpp: '770801001',
                ogrn: '1027700890123',
                address: 'г. Москва, Северный бульвар, д. 33',
                phone: '+7 (495) 890-12-34',
                email: '<EMAIL>',
                director: 'Новиков Алексей Викторович',
                status: 'active',
                syncStatus: 'error',
                lastSyncDate: '2024-01-08T11:15:00Z',
                createdDate: '2023-08-20T17:45:00Z'
            }
        ]
    },

    getOrganizations() {
        return Promise.resolve(this.getData());
    },

    getOrganizationById(organizationId) {
        const organizations = this.getData();
        const organization = organizations.find(o => o.id == organizationId);
        return Promise.resolve(organization);
    },

    getOrganizationsByIds(ids) {
        const organizations = this.getData();
        const filteredOrganizations = organizations.filter(org => ids.includes(org.id));
        return Promise.resolve(filteredOrganizations);
    },

    createOrganization(organizationData) {
        console.log('Creating organization:', organizationData);

        const newOrganization = {
            ...organizationData,
            id: Date.now(),
            status: 'active',
            syncStatus: 'never',
            lastSyncDate: null,
            createdDate: new Date().toISOString()
        };

        return Promise.resolve(newOrganization);
    },

    updateOrganization(organizationId, organizationData) {
        console.log('Updating organization:', organizationId, organizationData);

        const updatedOrganization = {
            ...organizationData,
            id: organizationId
        };

        return Promise.resolve(updatedOrganization);
    },

    deleteOrganization(organizationId) {
        console.log('Deleting organization:', organizationId);
        return Promise.resolve({ success: true });
    },

    syncOrganization(organizationId) {
        console.log('Syncing organization with 1C:', organizationId);

        // Имитация синхронизации
        return new Promise((resolve) => {
            setTimeout(() => {
                const success = Math.random() > 0.2; // 80% успеха
                resolve({
                    success,
                    message: success
                        ? 'Синхронизация с 1С выполнена успешно'
                        : 'Ошибка синхронизации с 1С: таймаут соединения',
                    syncDate: new Date().toISOString()
                });
            }, 2000); // Имитация задержки
        });
    },

    syncAllOrganizations() {
        console.log('Syncing all organizations with 1C');

        // Имитация массовой синхронизации
        return new Promise((resolve) => {
            setTimeout(() => {
                const totalCount = this.getData().length;
                const successCount = Math.floor(totalCount * 0.8); // 80% успеха
                const errorCount = totalCount - successCount;

                resolve({
                    success: true,
                    totalCount,
                    successCount,
                    errorCount,
                    message: `Синхронизация завершена. Успешно: ${successCount}, с ошибками: ${errorCount}`,
                    syncDate: new Date().toISOString()
                });
            }, 5000); // Имитация более длительной операции
        });
    }
}
