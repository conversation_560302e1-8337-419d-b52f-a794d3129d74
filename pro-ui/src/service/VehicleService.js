export const VehicleService = {
    getData: () => {
        return [
            {
                id: 1,
                registrationNumber: 'А123БВ777',
                model: 'ПАЗ-3205',
                manufacturer: 'ПАЗ',
                year: 2020,
                capacity: 25,
                fuelType: 'diesel',
                vin: 'XTT320500L0123456',
                organizationId: 1,
                organizationName: 'ООО "Городской транспорт"',
                status: 'active',
                lastMaintenanceDate: '2024-01-10T00:00:00Z',
                nextMaintenanceDate: '2024-04-10T00:00:00Z'
            },
            {
                id: 2,
                registrationNumber: 'В456ГД777',
                model: 'ЛиАЗ-5256',
                manufacturer: 'ЛиАЗ',
                year: 2019,
                capacity: 110,
                fuelType: 'gas',
                vin: 'XTL525600K0234567',
                organizationId: 1,
                organizationName: 'ООО "Городской транспорт"',
                status: 'active',
                lastMaintenanceDate: '2024-01-05T00:00:00Z',
                nextMaintenanceDate: '2024-04-05T00:00:00Z'
            },
            {
                id: 3,
                registrationNumber: 'Е789ЖЗ777',
                model: 'Электробус ЛиАЗ-6274',
                manufacturer: 'ЛиАЗ',
                year: 2022,
                capacity: 85,
                fuelType: 'electric',
                vin: 'XTL627400N0345678',
                organizationId: 2,
                organizationName: 'АО "Метрополитен"',
                status: 'active',
                lastMaintenanceDate: '2024-01-15T00:00:00Z',
                nextMaintenanceDate: '2024-04-15T00:00:00Z'
            },
            {
                id: 4,
                registrationNumber: 'И012КЛ777',
                model: 'ГАЗель Next',
                manufacturer: 'ГАЗ',
                year: 2021,
                capacity: 18,
                fuelType: 'gasoline',
                vin: 'XTH000000M0456789',
                organizationId: 3,
                organizationName: 'ООО "Автобусный парк №1"',
                status: 'active',
                lastMaintenanceDate: '2024-01-08T00:00:00Z',
                nextMaintenanceDate: '2024-04-08T00:00:00Z'
            },
            {
                id: 5,
                registrationNumber: 'М345НО777',
                model: 'НефАЗ-5299',
                manufacturer: 'НефАЗ',
                year: 2018,
                capacity: 105,
                fuelType: 'gas',
                vin: 'XTN529900J0567890',
                organizationId: 4,
                organizationName: 'ГУП "Мосгортранс"',
                status: 'maintenance',
                lastMaintenanceDate: '2024-01-20T00:00:00Z',
                nextMaintenanceDate: '2024-02-20T00:00:00Z'
            },
            {
                id: 6,
                registrationNumber: 'П678РС777',
                model: 'Hyundai County',
                manufacturer: 'Hyundai',
                year: 2020,
                capacity: 25,
                fuelType: 'diesel',
                vin: 'KMFWBH7BXLA678901',
                organizationId: 7,
                organizationName: 'ИП Лебедев М.И.',
                status: 'active',
                lastMaintenanceDate: '2024-01-12T00:00:00Z',
                nextMaintenanceDate: '2024-04-12T00:00:00Z'
            },
            {
                id: 7,
                registrationNumber: 'Т901УФ777',
                model: 'МАЗ-206',
                manufacturer: 'МАЗ',
                year: 2017,
                capacity: 95,
                fuelType: 'diesel',
                vin: 'XTM206000H0789012',
                organizationId: 8,
                organizationName: 'ООО "Грузоперевозки Север"',
                status: 'inactive',
                lastMaintenanceDate: '2023-12-15T00:00:00Z',
                nextMaintenanceDate: '2024-03-15T00:00:00Z'
            },
            {
                id: 8,
                registrationNumber: 'Х234ЦЧ777',
                model: 'Тролза-5265',
                manufacturer: 'Тролза',
                year: 2021,
                capacity: 100,
                fuelType: 'electric',
                vin: 'XTR526500M0890123',
                organizationId: 6,
                organizationName: 'ООО "Электротранс"',
                status: 'active',
                lastMaintenanceDate: '2024-01-18T00:00:00Z',
                nextMaintenanceDate: '2024-04-18T00:00:00Z'
            },
            {
                id: 9,
                registrationNumber: 'Ш567ЩЭ777',
                model: 'Volkswagen Crafter',
                manufacturer: 'Volkswagen',
                year: 2022,
                capacity: 19,
                fuelType: 'diesel',
                vin: 'WV1ZZZ2HZNX901234',
                organizationId: 5,
                organizationName: 'ООО "Такси Комфорт"',
                status: 'active',
                lastMaintenanceDate: '2024-01-14T00:00:00Z',
                nextMaintenanceDate: '2024-04-14T00:00:00Z'
            },
            {
                id: 10,
                registrationNumber: 'Ю890ЯА777',
                model: 'Iveco Daily',
                manufacturer: 'Iveco',
                year: 2019,
                capacity: 22,
                fuelType: 'diesel',
                vin: 'ZCFC35A0005012345',
                organizationId: 3,
                organizationName: 'ООО "Автобусный парк №1"',
                status: 'active',
                lastMaintenanceDate: '2024-01-06T00:00:00Z',
                nextMaintenanceDate: '2024-04-06T00:00:00Z'
            }
        ]
    },

    getVehicles() {
        return Promise.resolve(this.getData());
    },

    getVehiclesByProject(projectCode) {
        // В реальном приложении здесь будет фильтрация по проекту
        return Promise.resolve(this.getData());
    },

    getVehicleById(vehicleId) {
        const vehicles = this.getData();
        const vehicle = vehicles.find(v => v.id == vehicleId);
        return Promise.resolve(vehicle);
    },

    createVehicle(projectCode, vehicleData) {
        console.log('Creating vehicle for project:', projectCode, vehicleData);
        
        const newVehicle = {
            ...vehicleData,
            id: Date.now(),
            status: 'active',
            lastMaintenanceDate: null,
            nextMaintenanceDate: null
        };
        
        return Promise.resolve(newVehicle);
    },

    updateVehicle(vehicleId, vehicleData) {
        console.log('Updating vehicle:', vehicleId, vehicleData);
        
        const updatedVehicle = {
            ...vehicleData,
            id: vehicleId
        };
        
        return Promise.resolve(updatedVehicle);
    },

    deleteVehicle(vehicleId) {
        console.log('Deleting vehicle:', vehicleId);
        return Promise.resolve({ success: true });
    }
}
