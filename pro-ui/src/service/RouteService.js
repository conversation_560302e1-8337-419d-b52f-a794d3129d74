export const RouteService = {
    getData: () => {
        return [
            {
                id: 1,
                number: '101',
                name: 'Центр - Северный район',
                isCircular: false,
                projectId: 1,
                stations: [
                    { id: 1, order: 1 },
                    { id: 2, order: 2 },
                    { id: 8, order: 3 }
                ]
            },
            {
                id: 2,
                number: '102К',
                name: 'Кольцевой центральный',
                isCircular: true,
                projectId: 1,
                stations: [
                    { id: 1, order: 1 },
                    { id: 2, order: 2 },
                    { id: 3, order: 3 }
                ]
            },
            {
                id: 3,
                number: '205',
                name: 'Восточный - Западный',
                isCircular: false,
                projectId: 3,
                stations: [
                    { id: 6, order: 1 },
                    { id: 7, order: 2 }
                ]
            },
            {
                id: 4,
                number: '301',
                name: 'Экспресс до аэропорта',
                isCircular: false,
                projectId: 2,
                stations: [
                    { id: 4, order: 1 },
                    { id: 5, order: 2 }
                ]
            },
            {
                id: 5,
                number: '150',
                name: 'Пригородный маршрут',
                isCircular: false,
                projectId: 4,
                stations: []
            },
            {
                id: 6,
                number: '77К',
                name: 'Малое кольцо',
                isCircular: true,
                projectId: 5,
                stations: [
                    { id: 9, order: 1 },
                    { id: 10, order: 2 }
                ]
            },
            {
                id: 7,
                number: '88',
                name: 'Южный маршрут',
                isCircular: false,
                projectId: 7,
                stations: [
                    { id: 12, order: 1 }
                ]
            },
            {
                id: 8,
                number: '999',
                name: 'Ночной маршрут',
                isCircular: false,
                projectId: 8,
                stations: []
            }
        ]
    },

    getRoutes() {
        return Promise.resolve(this.getData());
    },

    getRoutesByProject(projectId) {
        const allRoutes = this.getData();
        const projectRoutes = allRoutes.filter(route => route.projectId == projectId);
        return Promise.resolve(projectRoutes);
    },

    async getRouteWithStations(routeId) {
        const routes = this.getData();
        const route = routes.find(r => r.id == routeId);

        if (!route || !route.stations || route.stations.length === 0) {
            return Promise.resolve(route);
        }

        // Получаем ID остановок из маршрута
        const stationIds = route.stations.map(s => s.id);

        // Импортируем StationService динамически
        const { StationService } = await import('./StationService.js');

        // Получаем полные данные остановок
        const stations = await StationService.getStationsByIds(stationIds);

        // Объединяем данные остановок с порядком из маршрута
        const stationsWithOrder = route.stations.map(routeStation => {
            const stationData = stations.find(s => s.id === routeStation.id);
            return {
                ...stationData,
                order: routeStation.order
            };
        }).filter(station => station.id); // Убираем остановки, которые не найдены

        return Promise.resolve({
            ...route,
            stations: stationsWithOrder
        });
    },

    async getRoutesByProjectWithStations(projectId) {
        const projectRoutes = await this.getRoutesByProject(projectId);

        // Получаем все маршруты с полными данными остановок
        const routesWithStations = await Promise.all(
            projectRoutes.map(route => this.getRouteWithStations(route.id))
        );

        return Promise.resolve(routesWithStations);
    },

    getRouteById(routeId) {
        const routes = this.getData();
        const route = routes.find(r => r.id == routeId);
        return Promise.resolve(route);
    },

    createRoute(projectCode, routeData) {
        // В реальном приложении здесь будет отправка POST запроса
        console.log('Creating route for project:', projectCode, routeData);

        const newRoute = {
            ...routeData,
            id: Date.now(), // Временный ID для демонстрации
            stations: routeData.stations || []
        };

        return Promise.resolve(newRoute);
    },

    updateRoute(routeId, routeData) {
        // В реальном приложении здесь будет отправка PUT запроса
        console.log('Updating route:', routeId, routeData);

        const updatedRoute = {
            ...routeData,
            id: routeId
        };

        return Promise.resolve(updatedRoute);
    },

    deleteRoute(routeId) {
        // В реальном приложении здесь будет отправка DELETE запроса
        console.log('Deleting route:', routeId);
        return Promise.resolve({ success: true });
    },

    updateRouteStations(routeId, stations) {
        // В реальном приложении здесь будет отправка PUT запроса для обновления остановок маршрута
        console.log('Updating route stations:', routeId, stations);
        return Promise.resolve({ success: true, stations });
    }
}
