spring:
  kafka:
    bootstrap-servers: ${KAFKA_SERVERS:**********:9092;***********:9092;**********:9092}
#    bootstrap-servers: ${KAFKA_SERVERS:************:9092,***********:9092,************:9092} #prod

s3:
  url: ${S3_URL:http://localhost:9000}
  access_key_id: ${S3_ACCESS_KEY_ID:s3__user}
  secret_access_key: ${S3_SECRET_ACCESS_KEY:s3__pass}
  region: ${S3_REGION:ru-moscow}
  bucket: ${S3_BUCKET:tkp3-manifest}

server:
  port: 8886